//
//  InsideNavigationView.swift
//  PopoversExample
//
//  Created by <PERSON><PERSON> (github.com/aheze) on 2/3/22.
//  Copyright © 2022 <PERSON><PERSON> <PERSON>. All rights reserved.
//

import SwiftUI

struct InsideNavigationView: View {
    var body: some View {
        NavigationLink(destination: NavigationDestinationView()) {
            ExampleTestingRow(
                image: "square.stack.3d.down.right.fill",
                title: "Inside Navigation View",
                color: 0x00AEEF
            )
        }
    }
}

struct NavigationDestinationView: View {
    @State var present = false

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                <PERSON><PERSON>("Present Popover") {
                    present.toggle()
                }

                NavigationLink("Next View", destination: Text("This view should appear with a swipe animation."))
            }
        }
        .cornerRadius(10)
        .padding()
        .background(Color(.secondarySystemBackground))
        .navigationBarTitleDisplayMode(.inline)
        .navigationViewStyle(.stack)
        .popover(present: $present) {
            Text("Popovers should work when attached to `NavigationView`s without interfering with system animations.")
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 1)
                .frame(maxWidth: 300)
        }
    }
}
