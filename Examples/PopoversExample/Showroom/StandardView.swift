//
//  StandardView.swift
//  PopoversExample
//
//  Created by <PERSON><PERSON> (github.com/aheze) on 12/23/21.
//  Copyright © 2022 <PERSON><PERSON> <PERSON>. All rights reserved.
//

import Popovers
import SwiftUI

struct StandardView: View {
    @State var present = false

    var body: some View {
        Button {
            present = true
        } label: {
            ExampleShowroomRow(color: UIColor(hex: 0x474747)) {
                HStack {
                    ExampleImage("arrowtriangle.down.fill", color: UIColor(hex: 0x474747))

                    Text("Standard")
                        .fontWeight(.medium)
                }
            }
        }
        .popover(
            present: $present,
            attributes: {
                $0.sourceFrameInset.top = -8
                $0.position = .absolute(
                    originAnchor: .top,
                    popoverAnchor: .bottom
                )
            }
        ) {
            Templates.Container(
                arrowSide: .bottom(.mostClockwise),
                backgroundColor: .green
            ) {
                Text("This is a pretty standard-looking popover with an arrow.")
                    .foregroundColor(.white)
            }
            .frame(maxWidth: 300)
        }
    }
}





